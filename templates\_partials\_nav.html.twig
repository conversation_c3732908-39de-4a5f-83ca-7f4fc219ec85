{# templates/navbar.html.twig #}
{% set current_route = app.request.attributes.get('_route') %}
{% set current_place = app.request.attributes.get('place') %}
{# Temporairement désactivé pour debug: {% set badges = navbar_badges() %} #}
{% set badges = {} %}

<nav id="navBar" class="navbar navbar-expand-lg navbar-main">
    <div class="container-fluid px-4">


        <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse"
                data-bs-target="#navbarSupportedContent"
                aria-controls="navbarSupportedContent" aria-expanded="false" aria-label="Toggle navigation">
            <i class="fas fa-bars text-white"></i>
        </button>

        <!-- Navigation -->
        <div class="collapse navbar-collapse" id="navbarSupportedContent">
            <!-- Navigation compacte -->
            <ul class="navbar-nav me-auto navigation-main">

                {# Packages – route spécifique #}
                <li class="nav-item {{ current_route == 'app_package' ? 'active' : '' }}" data-step="Packages">
                    <a class="nav-link {{ current_route == 'app_package' ? 'active' : '' }}" href="{{ path('app_package') }}">Packages</a>
                </li>

                {# Produit – même route avec paramètre "place" #}
                <li class="nav-item {{ current_route == 'app_document_place' and current_place == 'Produit' ? 'active' : '' }}" data-step="Produit">
                    <a class="nav-link {{ current_route == 'app_document_place' and current_place == 'Produit' ? 'active' : '' }}" href="{{ path('app_document_place', {'place': 'Produit'}) }}">
                        Product Management
                    </a>
                </li>

                {# Inventory (Qual_Logistique) – on considère aussi "Logistique" #}
                <li class="nav-item {{ current_route == 'app_document_place' and current_place in ['Qual_Logistique','Logistique'] ? 'active' : '' }}" data-step="Qual_Logistique">
                    <a class="nav-link {{ current_route == 'app_document_place' and current_place in ['Qual_Logistique','Logistique'] ? 'active' : '' }}" href="{{ path('app_document_place', {'place': 'Qual_Logistique'}) }}">
                        Inventory
                    </a>
                </li>

                {# Quality #}
                <li class="nav-item {{ current_route == 'app_document_place' and current_place == 'Quality' ? 'active' : '' }}" data-step="Quality">
                    <a class="nav-link {{ current_route == 'app_document_place' and current_place == 'Quality' ? 'active' : '' }}" href="{{ path('app_document_place', {'place': 'Quality'}) }}">
                        Quality
                    </a>
                </li>

                {# Project #}
                <li class="nav-item {{ current_route == 'app_document_place' and current_place == 'Project' ? 'active' : '' }}" data-step="Project">
                    <a class="nav-link {{ current_route == 'app_document_place' and current_place == 'Project' ? 'active' : '' }}" href="{{ path('app_document_place', {'place': 'Project'}) }}">
                        Project
                    </a>
                </li>

                {# Metro #}
                <li class="nav-item {{ current_route == 'app_document_place' and current_place == 'Metro' ? 'active' : '' }}" data-step="Metro">
                    <a class="nav-link {{ current_route == 'app_document_place' and current_place == 'Metro' ? 'active' : '' }}" href="{{ path('app_document_place', {'place': 'Metro'}) }}">
                        Metrologie
                    </a>
                </li>

                {# QProd #}
                <li class="nav-item {{ current_route == 'app_document_place' and current_place == 'QProd' ? 'active' : '' }}" data-step="QProd">
                    <a class="nav-link {{ current_route == 'app_document_place' and current_place == 'QProd' ? 'active' : '' }}" href="{{ path('app_document_place', {'place': 'QProd'}) }}">
                        Quality Prod
                    </a>
                </li>

                {# Dropdown Prod – pour Assembly, Machining, Molding #}
                <li class="nav-item dropdown {{ current_route == 'app_document_place' and current_place in ['Assembly','Machining','Molding'] ? 'active' : '' }}">
                    <a class="nav-link {{ current_route == 'app_document_place' and current_place in ['Assembly','Machining','Molding'] ? 'active' : '' }} dropdown-toggle" href="#" role="button" id="dropdownMenuProd" data-bs-toggle="dropdown" aria-expanded="false">
                       Prod
                    </a>
                    <ul class="dropdown-menu p-1" aria-labelledby="dropdownMenuProd">
                        <li data-step="Assembly" class="{{ current_route == 'app_document_place' and current_place == 'Assembly' ? 'active' : '' }}">
                            <a class="dropdown-item rounded-2" href="{{ path('app_document_place', {'place': 'Assembly'}) }}">
                                Assembly
                            </a>
                        </li>
                        <li data-step="Machining" class="{{ current_route == 'app_document_place' and current_place == 'Machining' ? 'active' : '' }}">
                            <a class="dropdown-item rounded-2" href="{{ path('app_document_place', {'place': 'Machining'}) }}">
                                Machining
                            </a>
                        </li>
                        <li data-step="Molding" class="{{ current_route == 'app_document_place' and current_place == 'Molding' ? 'active' : '' }}">
                            <a class="dropdown-item rounded-2" href="{{ path('app_document_place', {'place': 'Molding'}) }}">
                                Molding
                            </a>
                        </li>
                    </ul>
                </li>

                {# Tirage Plans #}
                <li class="nav-item {{ current_route == 'app_document_place' and current_place == 'Tirage_Plans' ? 'active' : '' }}" data-step="Tirage_Plans">
                    <a class="nav-link {{ current_route == 'app_document_place' and current_place == 'Tirage_Plans' ? 'active' : '' }}" href="{{ path('app_document_place', {'place': 'Tirage_Plans'}) }}">
                        Tirage Plans
                    </a>
                </li>


                {# Assemblage – Methode_assemblage #}
                <li class="nav-item {{ current_route == 'app_document_place' and current_place == 'Methode_assemblage' ? 'active' : '' }}" data-step="Methode_assemblage">
                    <a class="nav-link {{ current_route == 'app_document_place' and current_place == 'Methode_assemblage' ? 'active' : '' }}" href="{{ path('app_document_place', {'place': 'Methode_assemblage'}) }}">
                        Assy Method.
                    </a>
                </li>

                {# Dropdown Achats – pour Achat_Rfq, Achat_F30, Achat_FIA, Achat_RoHs_REACH, Achat_Hts #}
                <li class="nav-item dropdown {{ current_route == 'app_document_place' and current_place in ['Achat_Rfq','Achat_F30','Achat_FIA','Achat_RoHs_REACH','Achat_Hts'] ? 'active' : '' }}">
                    <a class="nav-link {{ current_route == 'app_document_place' and current_place in ['Achat_Rfq','Achat_F30','Achat_FIA','Achat_RoHs_REACH','Achat_Hts'] ? 'active' : '' }} dropdown-toggle" href="#" role="button" id="dropdownMenuAchats" data-bs-toggle="dropdown" aria-expanded="false">
                       Achats
                    </a>
                    <ul class="dropdown-menu p-1" aria-labelledby="dropdownMenuAchats">
                        <li data-step="Achat_Rfq" class="{{ current_route == 'app_document_place' and current_place == 'Achat_Rfq' ? 'active' : '' }}">
                            <a class="dropdown-item rounded-2" href="{{ path('app_document_place', {'place': 'Achat_Rfq'}) }}">
                                RFQ
                            </a>
                        </li>
                        <li data-step="Achat_F30" class="{{ current_route == 'app_document_place' and current_place == 'Achat_F30' ? 'active' : '' }}">
                            <a class="dropdown-item rounded-2" href="{{ path('app_document_place', {'place': 'Achat_F30'}) }}">
                                F30
                            </a>
                        </li>
                        <li data-step="Achat_FIA" class="{{ current_route == 'app_document_place' and current_place == 'Achat_FIA' ? 'active' : '' }}">
                            <a class="dropdown-item rounded-2" href="{{ path('app_document_place', {'place': 'Achat_FIA'}) }}">
                                FIA
                            </a>
                        </li>
                        <li data-step="Achat_RoHs_REACH" class="{{ current_route == 'app_document_place' and current_place == 'Achat_RoHs_REACH' ? 'active' : '' }}">
                            <a class="dropdown-item rounded-2" href="{{ path('app_document_place', {'place': 'Achat_RoHs_REACH'}) }}">
                                RoHs REACH
                            </a>
                        </li>
                        <li data-step="Achat_Hts" class="{{ current_route == 'app_document_place' and current_place == 'Achat_Hts' ? 'active' : '' }}">
                            <a class="dropdown-item rounded-2" href="{{ path('app_document_place', {'place': 'Achat_Hts'}) }}">
                                HTS
                            </a>
                        </li>
                    </ul>
                </li>

                {# Planning #}
                <li class="nav-item {{ current_route == 'app_document_place' and current_place == 'Planning' ? 'active' : '' }}" data-step="Planning">
                    <a class="nav-link {{ current_route == 'app_document_place' and current_place == 'Planning' ? 'active' : '' }}" href="{{ path('app_document_place', {'place': 'Planning'}) }}">
                        Logistics
                    </a>
                </li>

                {# Indus #}
                <li class="nav-item {{ current_route == 'app_document_place' and current_place == 'Indus' ? 'active' : '' }}" data-step="Indus">
                    <a class="nav-link {{ current_route == 'app_document_place' and current_place == 'Indus' ? 'active' : '' }}" href="{{ path('app_document_place', {'place': 'Indus'}) }}">
                        Assy. Routings
                    </a>
                </li>

                {# Dropdown SAP – pour Core_Data et Prod_Data #}
                <li class="nav-item dropdown {{ current_route == 'app_document_place' and current_place in ['Core_Data','Prod_Data'] ? 'active' : '' }}">
                    <a class="nav-link {{ current_route == 'app_document_place' and current_place in ['Core_Data','Prod_Data'] ? 'active' : '' }} dropdown-toggle" href="#" role="button" id="dropdownMenuSAP" data-bs-toggle="dropdown" aria-expanded="false">
                       SAP
                    </a>
                    <ul class="dropdown-menu p-1" aria-labelledby="dropdownMenuSAP">
                        <li data-step="Core_Data" class="{{ current_route == 'app_document_place' and current_place == 'Core_Data' ? 'active' : '' }}">
                            <a class="dropdown-item rounded-2" href="{{ path('app_document_place', {'place': 'Core_Data'}) }}">
                                Core Data
                            </a>
                        </li>
                        <li data-step="Prod_Data" class="{{ current_route == 'app_document_place' and current_place == 'Prod_Data' ? 'active' : '' }}">
                            <a class="dropdown-item rounded-2" href="{{ path('app_document_place', {'place': 'Prod_Data'}) }}">
                                Prod Data
                            </a>
                        </li>
                    </ul>
                </li>

                {# Labo – méthode labo #}
                <li class="nav-item {{ current_route == 'app_document_place' and current_place == 'methode_Labo' ? 'active' : '' }}" data-step="methode_Labo">
                    <a class="nav-link {{ current_route == 'app_document_place' and current_place == 'methode_Labo' ? 'active' : '' }}" href="{{ path('app_document_place', {'place': 'methode_Labo'}) }}">
                        Laboratory
                    </a>
                </li>

                {# GID #}
                <li class="nav-item {{ current_route == 'app_document_place' and current_place == 'GID' ? 'active' : '' }}" data-step="GID">
                    <a class="nav-link {{ current_route == 'app_document_place' and current_place == 'GID' ? 'active' : '' }}" href="{{ path('app_document_place', {'place': 'GID'}) }}">
                        Routing Entry
                    </a>
                </li>

                {# Costing #}
                <li class="nav-item {{ current_route == 'app_document_place' and current_place == 'Costing' ? 'active' : '' }}" data-step="Costing">
                    <a class="nav-link {{ current_route == 'app_document_place' and current_place == 'Costing' ? 'active' : '' }}" href="{{ path('app_document_place', {'place': 'Costing'}) }}">
                        Costing
                    </a>
                </li>

                {# Dropdown Gestion – pour Retour et Suivi des packages #}
                <li class="nav-item dropdown {{ current_route in ['retour','suivie_ref'] ? 'active' : '' }}">
                    <a class="nav-link {{ current_route in ['retour','suivie_ref'] ? 'active' : '' }} dropdown-toggle" href="#" role="button" id="dropdownMenuGestion" data-bs-toggle="dropdown" aria-expanded="false">
                       Gestion
                    </a>
                    <ul class="dropdown-menu p-1" aria-labelledby="dropdownMenuGestion">
                        <li data-step="retour" class="{{ current_route == 'retour' ? 'active' : '' }}">
                            <a class="dropdown-item rounded-2" href="{{ path('retour') }}">Retour</a>
                        </li>
                        <li data-step="suivie_ref" class="{{ current_route == 'suivie_ref' ? 'active' : '' }}">
                            <a class="dropdown-item rounded-2" href="{{ path('suivie_ref') }}">Suivi des packages</a>
                        </li>
                        <li data-step="time_tracking" class="{{ current_route == 'app_time_tracking' ? 'active' : '' }}">
                            <a class="dropdown-item rounded-2" href="{{ path('app_time_tracking') }}">Suivi des temps</a>
                        </li>
                        <li class="dropdown-divider"></li>
                        <li data-step="statistics" class="{{ current_route == 'app_statistics' ? 'active' : '' }}">
                            <a class="dropdown-item rounded-2" href="{{ path('app_statistics') }}">
                                <i class="fas fa-chart-line me-2"></i>Statistiques
                            </a>
                        </li>
                    </ul>
                </li>

                {# <li class="nav-item dropdown">
                    <a class="nav-link dropdown-toggle" href="#" id="dropdownMenuTimeStats" role="button" data-bs-toggle="dropdown" aria-expanded="false">
                        <i class="fas fa-clock"></i> Temps
                    </a>
                    {{ render(controller('App\\Controller\\TimeTrackingController::navbarStats')) }}
                </li> #}

            </ul>
        </div>

        <div class="d-flex align-items-center">
            {# <ul class="navbar-nav me-3">
                <li class="nav-item">
                    <a class="nav-link d-flex align-items-center special-dashboard-btn" href="{{ path('app_dashboard') }}" title="Tableau de bord">
                        <span class="nav-icon-wrapper">
                            <i class="fas fa-tachometer-alt text-warning"></i>
                        </span>
                        <span class="d-none d-lg-inline ms-1 fw-bold" style="color:rgb(65, 161, 116); text-shadow: 1px 1px 2px rgba(0,0,0,0.2);">Tableau de bord</span>
                    </a>
                </li>
            </ul> #}
            <style>
                .nav-icon-wrapper {
                    display: inline-flex;
                    align-items: center;
                    justify-content: center;
                    width: 24px;
                    height: 24px;
                    position: relative;
                }

                .navbar-nav .nav-link {
                    padding: 0.5rem 0.75rem;
                    border-radius: 4px;
                    transition: all 0.2s ease;
                }

                .navbar-nav .nav-link:hover {
                    background-color: rgba(255, 255, 255, 0.1);
                }
            </style>
            <div class="form-check form-switch">
                <input class="form-check-input" type="checkbox" id="aletiq">
                <label class="form-check-label text-white" for="aletiq">Plans</label>
            </div>
        </div>
    </div>
</nav>

{# Style personnalisé moderne #}
<style>
/* Navbar principale moderne */
.navbar-main {
    background: linear-gradient(135deg, #009BFF 0%, #0056b3 100%);
    box-shadow: 0 2px 20px rgba(0, 155, 255, 0.3);
    padding: 0.75rem 0;
}

/* Brand et logo */
.navbar-brand {
    text-decoration: none;
    color: white !important;
    font-weight: 600;
    font-size: 1.1rem;
}

.brand-title {
    background: linear-gradient(45deg, #fff, #e3f2fd);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Navigation compacte */
.navigation-main {
    gap: 0.5rem;
}

.navigation-main .nav-link {
    color: rgba(255, 255, 255, 0.9) !important;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: all 0.3s ease;
    position: relative;
}

.navigation-main .nav-link:hover {
    background: rgba(255, 255, 255, 0.1);
    color: white !important;
    transform: translateY(-1px);
}

.navigation-main .nav-link.active {
    background: rgba(255, 255, 255, 0.2);
    color: white !important;
    font-weight: 600;
    box-shadow: 0 2px 8px rgba(255, 255, 255, 0.3);
}

/* Dropdown moderne */
.dropdown-menu {
    background: rgba(255, 255, 255, 0.98);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 12px;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
    padding: 0.5rem;
    margin-top: 0.5rem;
}

.dropdown-item {
    border-radius: 8px;
    padding: 0.5rem 1rem;
    transition: all 0.2s ease;
    color: #374151;
    margin: 0.1rem 0;
}

.dropdown-item:hover {
    background: rgba(0, 155, 255, 0.1);
    color: #009BFF;
    transform: translateX(2px);
}

.dropdown-menu li.active > .dropdown-item {
    background: rgba(0, 155, 255, 0.2);
    color: #009BFF !important;
    font-weight: 600;
}

/* Switch Plans */
.form-check-input:checked {
    background-color: #28a745 !important;
    border-color: #28a745 !important;
}

.form-check-label {
    color: white;
    font-weight: 500;
}

/* Bouton mobile */
.navbar-toggler {
    border: none;
    padding: 0.5rem;
}

.navbar-toggler:focus {
    box-shadow: none;
}

/* Responsive */
@media (max-width: 991.98px) {
    .navigation-main {
        padding-top: 1rem;
        gap: 0.25rem;
    }

    .navigation-main .nav-link {
        margin: 0.25rem 0;
    }
}

/* Animation d'entrée */
@keyframes fadeInDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.navbar-main {
    animation: fadeInDown 0.3s ease-out;
}

/* Effet de survol sur les icônes */
.nav-link i {
    transition: transform 0.2s ease;
}

.nav-link:hover i {
    transform: scale(1.1);
}
</style>

<script>
    $(document).ready(function() {

        let storedState = localStorage.getItem('aletiq');

        // Si 'true', on coche le switch
        if (storedState === 'true') {
            $('#aletiq').prop('checked', true);
        }

        // Au changement d'état, on enregistre dans le localStorage
       $('#aletiq').on('change', function () {
            const isChecked = $(this).is(':checked');
            localStorage.setItem('aletiq', isChecked);

            if (isChecked) {
                $('.preview-tooltip').each(function () {
                    const url = $(this).attr('href');
                    tippy(this, {
                        content: `<iframe src="${url}" width="800" height="500" style="border: none;"></iframe>`,
                        allowHTML: true,
                        interactive: true,
                        placement: 'right',
                        theme: 'light-border'
                    });
                });
            } else {
                // Détruire tous les tooltips Tippy actifs
                document.querySelectorAll('.preview-tooltip').forEach(el => {
                    if (el._tippy) {
                        el._tippy.destroy();
                    }
                });
            }
        });

        // AJAX simple pour les badges - SANS CACHE CLIENT
        $.ajax({
            url: "{{ path('count_document') }}",
            method: 'GET',
            dataType: 'json',
            timeout: 10000,
            success: function(data) {
                // Ajout des badges sur chaque élément individuel
                $.each(data, function(step, count) {
                    if (count > 0) {
                        const $element = $('[data-step="' + step + '"]');
                        if ($element.length) {
                            const $badge = $('<span>')
                                .addClass('badge rounded-pill bg-success position-absolute')
                                .css({
                                    top: '8px',
                                    right: '0px',
                                    transform: 'translate(50%, -50%)',
                                    fontSize: '0.65rem'
                                })
                                .text(count);
                            $element.append($badge);
                        }
                    }
                });

                // Pour chaque dropdown, sommer les valeurs
                $('.nav-item.dropdown').each(function() {
                    let total = 0;
                    $(this).find('li[data-step]').each(function() {
                        const step = $(this).data('step');
                        if (data[step] && data[step] > 0) {
                            total += data[step];
                        }
                    });

                    if (total > 0) {
                        const $parentLink = $(this).children('a.dropdown-toggle');
                        const $badge = $('<span>')
                            .addClass('badge rounded-pill bg-success position-absolute')
                            .css({
                                top: '8px',
                                right: '0px',
                                transform: 'translate(50%, -50%)',
                                fontSize: '0.65rem'
                            })
                            .text(total);
                        $parentLink.append($badge);
                    }
                });

                console.log('Badges appliqués:', Object.keys(data).length);
            },
            error: function(xhr, status, error) {
                console.error('Erreur chargement badges:', error);
            }
        });
    });
</script>
