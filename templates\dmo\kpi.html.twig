{% extends 'base.html.twig' %}

{% block title %}KPI / Key Indicators{% endblock %}

{% block navbar %}
    {% include '_partials/_nav2.html.twig' %}
{% endblock %}

{% block body %}
<div class="kpi-dashboard">
    <!-- Header moderne -->
    <div class="dashboard-header">
        <div class="container-fluid px-4">
            <div class="row align-items-center">
                <div class="col">
                    <h1 class="dashboard-title">
                        <i class="fas fa-chart-line me-3"></i>
                        Tableau de Bord DMO
                    </h1>
                    <p class="dashboard-subtitle">Indicateurs clés de performance et analyses</p>
                </div>
                <div class="col-auto">
                    <div class="stats-summary">
                        <div class="stat-card">
                            <div class="stat-number">{{ dataTableOpenEng|length + dataTableOpenOther|length }}</div>
                            <div class="stat-label">DMO Ouverts</div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-number">{{ dataTableClosedEng|length + dataTableClosedOther|length }}</div>
                            <div class="stat-label">DMO Fermés</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="container-fluid px-4 py-4">
        <!-- Navigation moderne avec pills -->
        <div class="nav-pills-container mb-4">
            <ul class="nav nav-pills nav-pills-modern" id="dmoTab" role="tablist">
                <li class="nav-item" role="presentation">
                    <button class="nav-link active" id="open-eng-tab" data-bs-toggle="pill" data-bs-target="#open-eng" type="button" role="tab" aria-controls="open-eng" aria-selected="true">
                        <i class="fas fa-folder-open me-2"></i>
                        Engineering - Ouverts
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="closed-eng-tab" data-bs-toggle="pill" data-bs-target="#closed-eng" type="button" role="tab" aria-controls="closed-eng" aria-selected="false">
                        <i class="fas fa-check-circle me-2"></i>
                        Engineering - Fermés
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="open-other-tab" data-bs-toggle="pill" data-bs-target="#open-other" type="button" role="tab" aria-controls="open-other" aria-selected="false">
                        <i class="fas fa-folder-open me-2"></i>
                        Autres - Ouverts
                    </button>
                </li>
                <li class="nav-item" role="presentation">
                    <button class="nav-link" id="closed-other-tab" data-bs-toggle="pill" data-bs-target="#closed-other" type="button" role="tab" aria-controls="closed-other" aria-selected="false">
                        <i class="fas fa-check-circle me-2"></i>
                        Autres - Fermés
                    </button>
                </li>
            </ul>
        </div>

        <!-- Contenu des onglets -->
        <div class="tab-content" id="dmoTabContent">
            <!-- Open DMOs Engineering -->
            <div class="tab-pane fade show active" id="open-eng" role="tabpanel" aria-labelledby="open-eng-tab">
                <div class="kpi-section">
                    <div class="section-header">
                        <h3 class="section-title">DMO Engineering - Ouverts</h3>
                        <p class="section-description">
                            Nombre de DMO ouverts par date d'émission sur les 3 dernières années,
                            répartis par division avec cumul annuel.
                        </p>
                    </div>

                    <div class="row">
                        <!-- Graphique -->
                        <div class="col-lg-8">
                            <div class="chart-card">
                                <div class="chart-header">
                                    <h5 class="chart-title">
                                        <i class="fas fa-chart-bar me-2"></i>
                                        Évolution mensuelle
                                    </h5>
                                    <div class="chart-controls">
                                        <button class="btn btn-sm btn-outline-primary" onclick="toggleChartType('openEngChart')">
                                            <i class="fas fa-exchange-alt"></i>
                                        </button>
                                    </div>
                                </div>
                                <div class="chart-container">
                                    <canvas id="openEngChart"></canvas>
                                </div>
                            </div>
                        </div>

                        <!-- Tableau de données -->
                        <div class="col-lg-4">
                            <div class="data-card">
                                <div class="data-header">
                                    <h5 class="data-title">
                                        <i class="fas fa-table me-2"></i>
                                        Données détaillées
                                    </h5>
                                </div>
                                <div class="data-table-container">
                                    <table class="table table-modern">
                                        <thead>
                                            <tr>
                                                <th>Période</th>
                                                <th>Energy</th>
                                                <th>Industry</th>
                                                <th>Aero.</th>
                                                <th>Cumul</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            {% for row in dataTableOpenEng %}
                                                <tr>
                                                    <td class="period-cell">{{ row.periode }}</td>
                                                    <td class="number-cell energy">{{ row.Energy }}</td>
                                                    <td class="number-cell industry">{{ row.Industry }}</td>
                                                    <td class="number-cell aerospace">{{ row.Aerospace }}</td>
                                                    <td class="number-cell cumul">{{ row.Cumul }}</td>
                                                </tr>
                                            {% endfor %}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

        <!-- Closed DMOs Engineering -->
        <div class="tab-pane fade" id="closed-eng" role="tabpanel" aria-labelledby="closed-eng-tab">
            <h2 class="mt-3">Closed DMOs (Engineering)</h2>
                <p class="description">
                    Number of DMO closed each month of the last 3 years.
                    A yearly cumulative total shows up as the curve on the graph.
                </p>
            <div class="content">
                <div class="table-container">
                    <table class="styled-table">
                        <thead>
                            <tr>
                                <th>Period</th>
                                <th>Eng. Energy</th>
                                <th>Eng. Industry</th>
                                <th>Eng. Aero.</th>
                                <th>Cumul</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for row in dataTableClosedEng %}
                                <tr>
                                    <td>{{ row.periode }}</td>
                                    <td>{{ row.Energy }}</td>
                                    <td>{{ row.Industry }}</td>
                                    <td>{{ row.Aerospace }}</td>
                                    <td>{{ row.Cumul }}</td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="chart-container">
                    <div id="chart_closed_engineering"></div>
                </div>
            </div>
        </div>

        <!-- Open DMOs Non-Engineering -->
        <div class="tab-pane fade" id="open-other" role="tabpanel" aria-labelledby="open-other-tab">
            <h2 class="mt-3">Open DMOs (Non-Engineering)</h2>
            <p class="description">
                Number of DMO opened per issue date each year, month and division over the current year and the last 3 years.
                A yearly cumulative total shows up as the curve on the graph.
            </p>
            <div class="content">
                <div class="table-container">
                    <table class="styled-table">
                        <thead>
                            <tr>
                                <th>Period</th>
                                <th>Other Energy</th>
                                <th>Other Industry</th>
                                <th>Other Aero.</th>
                                <th>Cumul</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for row in dataTableOpenOther %}
                                <tr>
                                    <td>{{ row.periode }}</td>
                                    <td>{{ row.Energy }}</td>
                                    <td>{{ row.Industry }}</td>
                                    <td>{{ row.Aerospace }}</td>
                                    <td>{{ row.Cumul }}</td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="chart-container">
                    <div id="chart_open_other"></div>
                </div>
            </div>
        </div>

        <!-- Closed DMOs Non-Engineering -->
        <div class="tab-pane fade" id="closed-other" role="tabpanel" aria-labelledby="closed-other-tab">
            <h2 class="mt-3">Closed DMOs (Non-Engineering)</h2>
            <p class="description">
                Number of DMO closed each month of the last 3 years.
                A yearly cumulative total shows up as the curve on the graph.
            </p>
            <div class="content">
                <div class="table-container">
                    <table class="styled-table">
                        <thead>
                            <tr>
                                <th>Period</th>
                                <th>Other Energy</th>
                                <th>Other Industry</th>
                                <th>Other Aero.</th>
                                <th>Cumul</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for row in dataTableClosedOther %}
                                <tr>
                                    <td>{{ row.periode }}</td>
                                    <td>{{ row.Energy }}</td>
                                    <td>{{ row.Industry }}</td>
                                    <td>{{ row.Aerospace }}</td>
                                    <td>{{ row.Cumul }}</td>
                                </tr>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                <div class="chart-container">
                    <div id="chart_closed_other"></div>
                </div>
            </div>
        </div>
    </div>

<!-- Chart.js -->
<script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
<script src="https://cdn.jsdelivr.net/npm/chartjs-adapter-date-fns/dist/chartjs-adapter-date-fns.bundle.min.js"></script>

<!-- Styles modernes pour le dashboard -->
<style>
/* Dashboard moderne */
.kpi-dashboard {
    background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
    min-height: 100vh;
}

/* Header du dashboard */
.dashboard-header {
    background: linear-gradient(135deg, #009BFF 0%, #0056b3 100%);
    color: white;
    padding: 2rem 0;
    box-shadow: 0 4px 20px rgba(0, 155, 255, 0.3);
}

.dashboard-title {
    font-size: 2.5rem;
    font-weight: 700;
    margin: 0;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.dashboard-subtitle {
    font-size: 1.1rem;
    opacity: 0.9;
    margin: 0;
}

/* Stats summary */
.stats-summary {
    display: flex;
    gap: 1.5rem;
}

.stat-card {
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
    border-radius: 16px;
    padding: 1.5rem;
    text-align: center;
    min-width: 120px;
}

.stat-number {
    font-size: 2rem;
    font-weight: 700;
    color: white;
}

.stat-label {
    font-size: 0.9rem;
    opacity: 0.9;
    margin-top: 0.5rem;
}

/* Navigation pills moderne */
.nav-pills-container {
    background: white;
    border-radius: 16px;
    padding: 1rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
}

.nav-pills-modern .nav-link {
    background: transparent;
    border: 2px solid transparent;
    border-radius: 12px;
    color: #6c757d;
    font-weight: 500;
    padding: 0.75rem 1.5rem;
    margin: 0 0.25rem;
    transition: all 0.3s ease;
}

.nav-pills-modern .nav-link:hover {
    background: rgba(0, 155, 255, 0.1);
    color: #009BFF;
    transform: translateY(-2px);
}

.nav-pills-modern .nav-link.active {
    background: linear-gradient(135deg, #009BFF, #0056b3);
    color: white;
    border-color: #009BFF;
    box-shadow: 0 4px 15px rgba(0, 155, 255, 0.3);
}

/* Sections KPI */
.kpi-section {
    background: white;
    border-radius: 16px;
    padding: 2rem;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.1);
    margin-bottom: 2rem;
}

.section-header {
    margin-bottom: 2rem;
    text-align: center;
}

.section-title {
    color: #2d3748;
    font-weight: 600;
    margin-bottom: 0.5rem;
}

.section-description {
    color: #718096;
    font-size: 1rem;
    margin: 0;
}

/* Cartes graphiques */
.chart-card, .data-card {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    overflow: hidden;
    height: 100%;
}

.chart-header, .data-header {
    background: linear-gradient(135deg, #f8f9fa, #e9ecef);
    padding: 1rem 1.5rem;
    border-bottom: 1px solid #dee2e6;
    display: flex;
    justify-content: between;
    align-items: center;
}

.chart-title, .data-title {
    color: #2d3748;
    font-weight: 600;
    margin: 0;
    font-size: 1rem;
}

.chart-controls {
    display: flex;
    gap: 0.5rem;
}

.chart-container {
    padding: 1.5rem;
    height: 400px;
    position: relative;
}

/* Tableau moderne */
.data-table-container {
    max-height: 400px;
    overflow-y: auto;
}

.table-modern {
    margin: 0;
    font-size: 0.9rem;
}

.table-modern th {
    background: #f8f9fa;
    color: #495057;
    font-weight: 600;
    border: none;
    padding: 0.75rem 0.5rem;
    font-size: 0.8rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.table-modern td {
    border: none;
    padding: 0.75rem 0.5rem;
    vertical-align: middle;
}

.period-cell {
    font-weight: 600;
    color: #2d3748;
}

.number-cell {
    text-align: center;
    font-weight: 500;
}

.number-cell.energy { color: #e53e3e; }
.number-cell.industry { color: #3182ce; }
.number-cell.aerospace { color: #38a169; }
.number-cell.cumul {
    color: #d69e2e;
    font-weight: 700;
    background: rgba(214, 158, 46, 0.1);
}

/* Responsive */
@media (max-width: 991.98px) {
    .dashboard-title {
        font-size: 2rem;
    }

    .stats-summary {
        flex-direction: column;
        gap: 1rem;
    }

    .nav-pills-modern .nav-link {
        margin: 0.25rem 0;
        text-align: center;
    }

    .chart-container {
        height: 300px;
    }
}
</style>

<script>
// Configuration globale Chart.js
Chart.defaults.font.family = "'Inter', sans-serif";
Chart.defaults.color = '#6c757d';

// Couleurs du thème
const colors = {
    energy: '#e53e3e',
    industry: '#3182ce',
    aerospace: '#38a169',
    cumul: '#d69e2e',
    primary: '#009BFF'
};

// Variables globales pour les graphiques
let charts = {};

// Initialisation des graphiques
document.addEventListener('DOMContentLoaded', function() {
    initializeCharts();
});

function initializeCharts() {
    createOpenEngChart();
    createClosedEngChart();
    createOpenOtherChart();
    createClosedOtherChart();
}

// Fonction pour créer le graphique DMO Engineering Ouverts
function createOpenEngChart() {
    const ctx = document.getElementById('openEngChart');
    if (!ctx) return;

    const data = {
        labels: [
            {% for row in dataTableOpenEng %}
                '{{ row.periode }}'{{ not loop.last ? ',' : '' }}
            {% endfor %}
        ],
        datasets: [
            {
                label: 'Energy',
                data: [
                    {% for row in dataTableOpenEng %}
                        {{ row.Energy }}{{ not loop.last ? ',' : '' }}
                    {% endfor %}
                ],
                backgroundColor: colors.energy + '80',
                borderColor: colors.energy,
                borderWidth: 2,
                type: 'bar'
            },
            {
                label: 'Industry',
                data: [
                    {% for row in dataTableOpenEng %}
                        {{ row.Industry }}{{ not loop.last ? ',' : '' }}
                    {% endfor %}
                ],
                backgroundColor: colors.industry + '80',
                borderColor: colors.industry,
                borderWidth: 2,
                type: 'bar'
            },
            {
                label: 'Aerospace',
                data: [
                    {% for row in dataTableOpenEng %}
                        {{ row.Aerospace }}{{ not loop.last ? ',' : '' }}
                    {% endfor %}
                ],
                backgroundColor: colors.aerospace + '80',
                borderColor: colors.aerospace,
                borderWidth: 2,
                type: 'bar'
            },
            {
                label: 'Cumul',
                data: [
                    {% for row in dataTableOpenEng %}
                        {{ row.Cumul }}{{ not loop.last ? ',' : '' }}
                    {% endfor %}
                ],
                backgroundColor: 'transparent',
                borderColor: colors.cumul,
                borderWidth: 3,
                type: 'line',
                yAxisID: 'y1',
                tension: 0.4,
                pointBackgroundColor: colors.cumul,
                pointBorderColor: '#fff',
                pointBorderWidth: 2,
                pointRadius: 6
            }
        ]
    };

    const config = {
        type: 'bar',
        data: data,
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                title: {
                    display: true,
                    text: 'DMO Engineering - Ouverts',
                    font: { size: 16, weight: 'bold' },
                    color: '#2d3748'
                },
                legend: {
                    position: 'top',
                    labels: {
                        usePointStyle: true,
                        padding: 20
                    }
                }
            },
            scales: {
                x: {
                    title: {
                        display: true,
                        text: 'Période'
                    },
                    grid: {
                        display: false
                    }
                },
                y: {
                    type: 'linear',
                    display: true,
                    position: 'left',
                    title: {
                        display: true,
                        text: 'DMO Mensuels'
                    },
                    grid: {
                        color: 'rgba(0,0,0,0.1)'
                    }
                },
                y1: {
                    type: 'linear',
                    display: true,
                    position: 'right',
                    title: {
                        display: true,
                        text: 'Cumul'
                    },
                    grid: {
                        drawOnChartArea: false,
                    },
                }
            },
            interaction: {
                mode: 'index',
                intersect: false,
            }
        }
    };

    charts.openEng = new Chart(ctx, config);
}

// Fonction pour basculer entre les types de graphiques
function toggleChartType(chartId) {
    const chart = charts[chartId.replace('Chart', '')];
    if (!chart) return;

    const currentType = chart.config.type;
    const newType = currentType === 'bar' ? 'line' : 'bar';

    // Mettre à jour le type de graphique
    chart.config.type = newType;

    // Ajuster les datasets selon le type
    chart.data.datasets.forEach((dataset, index) => {
        if (index < 3) { // Les 3 premiers datasets (Energy, Industry, Aerospace)
            dataset.type = newType;
            if (newType === 'line') {
                dataset.backgroundColor = 'transparent';
                dataset.tension = 0.4;
                dataset.pointRadius = 4;
                dataset.pointBackgroundColor = dataset.borderColor;
            } else {
                dataset.backgroundColor = dataset.borderColor + '80';
                dataset.tension = 0;
                dataset.pointRadius = 0;
            }
        }
    });

    chart.update();
}

// Fonctions pour les autres graphiques (simplifiées pour l'exemple)
function createClosedEngChart() {
    // Implémentation similaire pour les DMO fermés Engineering
    console.log('Closed Engineering chart would be created here');
}

function createOpenOtherChart() {
    // Implémentation similaire pour les DMO ouverts Non-Engineering
    console.log('Open Other chart would be created here');
}

function createClosedOtherChart() {
    // Implémentation similaire pour les DMO fermés Non-Engineering
    console.log('Closed Other chart would be created here');
}

// Gestion des onglets
document.querySelectorAll('button[data-bs-toggle="pill"]').forEach(function(tab) {
    tab.addEventListener('shown.bs.tab', function(event) {
        // Redimensionner les graphiques quand l'onglet devient visible
        Object.values(charts).forEach(chart => {
            if (chart && typeof chart.resize === 'function') {
                chart.resize();
            }
        });
    });
});

</script>
    </div>
</div>
{% endblock %}
