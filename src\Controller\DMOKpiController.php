<?php
// src/Controller/DMOKpiController.php

namespace App\Controller;

use Doctrine\ORM\EntityManagerInterface;
use Symfony\Bundle\FrameworkBundle\Controller\AbstractController;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Annotation\Route;

class DMOKpiController extends AbstractController
{
    #[Route('/dmo/kpi', name: 'app_dmo_kpi')]
    public function index(EntityManagerInterface $entityManager): Response
    {
        $connection = $entityManager->getConnection();

        /*
         * REQUÊTE 1 : DMO ouvertes de type Engineering (3 dernières années)
         */
        $sql1 = "
            SELECT
                YEAR(d.date_init) AS FY,
                DATE_FORMAT(d.date_init, '%b') AS Month,
                COUNT(CASE WHEN pr.division = 'Energy'   THEN 1 END) AS Energy,
                COUNT(CASE WHEN pr.division = 'Industry' THEN 1 END) AS Industry,
                COUNT(CASE WHEN pr.division = 'Aerospace' THEN 1 END) AS Aerospace
            FROM
                dmo d
            LEFT JOIN product_range pr ON d.product_range_id = pr.id
            WHERE
                (YEAR(NOW()) - YEAR(d.date_init)) <= 3
                AND d.type = 'Engineering'
            GROUP BY
                YEAR(d.date_init),
                MONTH(d.date_init)
            ORDER BY
                YEAR(d.date_init) ASC,
                MONTH(d.date_init) ASC
        ";

        $openEngData = $connection->fetchAllAssociative($sql1);

        /*
         * REQUÊTE 2 : DMO fermées de type Engineering (3 dernières années)
         */
        $sql2 = "
            SELECT
                YEAR(d.date_end) AS FY,
                DATE_FORMAT(d.date_end, '%b') AS Month,
                COUNT(CASE WHEN pr.division = 'Energy'   THEN 1 END) AS Energy,
                COUNT(CASE WHEN pr.division = 'Industry' THEN 1 END) AS Industry,
                COUNT(CASE WHEN pr.division = 'Aerospace' THEN 1 END) AS Aerospace
            FROM
                dmo d
            LEFT JOIN product_range pr ON d.product_range_id = pr.id
            WHERE
                d.status = 0
                AND d.date_end IS NOT NULL
                AND (YEAR(NOW()) - YEAR(d.date_init)) <= 3
                AND d.type = 'Engineering'
            GROUP BY
                YEAR(d.date_end),
                MONTH(d.date_end)
            ORDER BY
                YEAR(d.date_end) ASC,
                MONTH(d.date_end) ASC
        ";

        $closedEngData = $connection->fetchAllAssociative($sql2);

        /*
         * REQUÊTE 3 : DMO ouvertes Method Assy. et Method Lab. (3 dernières années)
         */
        $sql3 = "
            SELECT
                YEAR(d.date_init) AS FY,
                DATE_FORMAT(d.date_init, '%b') AS Month,
                COUNT(CASE WHEN d.type = 'Method Assy.' THEN 1 END) AS 'Method_Assy',
                COUNT(CASE WHEN d.type = 'Method Lab.' THEN 1 END) AS 'Method_Lab'
            FROM
                dmo d
            WHERE
                (YEAR(NOW()) - YEAR(d.date_init)) <= 3
                AND d.type IN ('Method Assy.', 'Method Lab.')
            GROUP BY
                YEAR(d.date_init),
                MONTH(d.date_init)
            ORDER BY
                YEAR(d.date_init) ASC,
                MONTH(d.date_init) ASC
        ";

        $openOtherData = $connection->fetchAllAssociative($sql3);

        /*
         * REQUÊTE 4 : DMO fermées Method Assy. et Method Lab. (3 dernières années)
         */
        $sql4 = "
            SELECT
                YEAR(t2.End_Date) AS FY,
                DATE_FORMAT(t2.End_Date, '%b') AS Month,
                COUNT(CASE WHEN t2.Type = 'Method Assy.' THEN 1 END) AS 'Method_Assy',
                COUNT(CASE WHEN t2.Type = 'Method Lab.' THEN 1 END) AS 'Method_Lab'
            FROM
                db_dmo.tbl_dmo AS t2
            WHERE
                t2.Status = 'Closed'
                AND (YEAR(NOW()) - YEAR(t2.Issue_Date)) <= 3
                AND t2.Type IN ('Method Assy.', 'Method Lab.')
            GROUP BY
                YEAR(t2.End_Date),
                MONTH(t2.End_Date)
            ORDER BY
                YEAR(t2.End_Date) ASC,
                MONTH(t2.End_Date) ASC
        ";

        $closedOtherData = $dmoConnection->fetchAllAssociative($sql4);

        // Traitement des données pour les tableaux
        $dataTableOpenEng = $this->processEngData($openEngData, 'open');
        $dataTableClosedEng = $this->processEngData($closedEngData, 'closed');
        $dataTableOpenOther = $this->processOtherData($openOtherData, 'open');
        $dataTableClosedOther = $this->processOtherData($closedOtherData, 'closed');

        return $this->render('dmo/kpi.html.twig', [
            'dataTableOpenEng'   => $dataTableOpenEng,
            'dataTableClosedEng' => $dataTableClosedEng,
            'dataTableOpenOther'   => $dataTableOpenOther,
            'dataTableClosedOther' => $dataTableClosedOther,
        ]);
    }

    private function processEngData(array $data, string $type): array
    {
        $result = [];
        $cumul = 0;

        foreach ($data as $row) {
            $total = (int)$row['Energy'] + (int)$row['Industry'] + (int)$row['Aerospace'];
            $cumul += $total;

            $result[] = [
                'periode' => $row['FY'] . '-' . $row['Month'],
                'Energy' => (int)$row['Energy'],
                'Industry' => (int)$row['Industry'],
                'Aerospace' => (int)$row['Aerospace'],
                'Cumul' => $cumul
            ];
        }

        return $result;
    }

    private function processOtherData(array $data, string $type): array
    {
        $result = [];
        $cumul = 0;

        foreach ($data as $row) {
            $total = (int)$row['Method_Assy'] + (int)$row['Method_Lab'];
            $cumul += $total;

            $result[] = [
                'periode' => $row['FY'] . '-' . $row['Month'],
                'Energy' => (int)$row['Method_Assy'],    // On utilise Energy pour Method Assy
                'Industry' => (int)$row['Method_Lab'],   // On utilise Industry pour Method Lab
                'Aerospace' => 0,                        // Pas d'Aerospace pour les Methods
                'Cumul' => $cumul
            ];
        }

        return $result;
    }
}
