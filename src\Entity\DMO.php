<?php

namespace App\Entity;

use App\Repository\DMORepository;
use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\DBAL\Types\Types;
use Doctrine\ORM\Mapping as ORM;

#[ORM\Entity(repositoryClass: DMORepository::class)]
class DMO
{
    #[ORM\Id]
    #[ORM\GeneratedValue]
    #[ORM\Column]
    private ?int $id = null;

    #[ORM\Column]
    private ?\DateTimeImmutable $date_init = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $Description = null;

    #[ORM\ManyToOne(inversedBy: 'dMOs')]
    private ?User $requestor = null;

    #[ORM\Column(length: 255)]
    private ?string $Decision = null;

    #[ORM\Column]
    private ?bool $status = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $Ex = null;

    // #[ORM\Column(nullable: true)]
    // private ?bool $Ex = null;

    #[ORM\Column(nullable: true)]
    private ?bool $Indus_Related = null;

    #[ORM\ManyToOne(inversedBy: 'dMos_Eng_Owner')]
    private ?User $Eng_Owner = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE, nullable: true)]
    private ?\DateTimeInterface $date_end = null;

    #[ORM\Column(nullable: true)]
    private ?int $Pr_Number = null;

    /**
     * @var Collection<int, Commentaire>
     */
    #[ORM\OneToMany(targetEntity: Commentaire::class, mappedBy: 'DmoId')]
    private Collection $commentaires;

    #[ORM\ManyToOne(inversedBy: 'Last_Modificator_Dmos')]
    private ?User $last_Modificator = null;

    #[ORM\Column(type: Types::DATETIME_MUTABLE)]
    private ?\DateTimeInterface $last_Update_Date = null;

    #[ORM\Column(type: Types::TEXT, nullable: true)]
    private ?string $Ex_Assessment = null;

    #[ORM\Column]
    private ?int $Spent_Time = null;

    #[ORM\Column(length: 255)]
    private ?string $Type = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $Document = null;

    #[ORM\ManyToOne(targetEntity: ProductRange::class, inversedBy: 'DMO')]
    #[ORM\JoinColumn(nullable: false)]
    private ?ProductRange $productRange = null;

    #[ORM\ManyToOne(inversedBy: 'dmo')]
    private ?Project $project_relation = null;

    /**
     * @var Collection<int, ReleasedPackage>
     */
    #[ORM\ManyToMany(targetEntity: ReleasedPackage::class, mappedBy: 'dmos')]
    private Collection $releasedPackages;

    #[ORM\Column(nullable: true)]
    private ?int $legacy_id = null;

    #[ORM\Column(length: 255, nullable: true)]
    private ?string $dmo = null;

    public function __construct()
    {
        $this->commentaires = new ArrayCollection();
        $this->releasedPackages = new ArrayCollection();
    }

    public function getId(): ?int
    {
        // return 'DMO'.$this->id;
        return $this->id;
    }

    public function getDmoId(): ?string
    {
        // Si le champ dmo n'est pas défini, générer un identifiant temporaire
        if ($this->dmo === null && $this->id !== null) {
            return 'DMO' . $this->id;
        }
        return $this->dmo;
    }

    public function getDateInit(): ?\DateTimeImmutable
    {
        return $this->date_init;
    }

    public function setDateInit(\DateTimeImmutable $date_init): static
    {
        $this->date_init = $date_init;

        return $this;
    }

    public function getDescription(): ?string
    {
        return $this->Description;
    }

    public function setDescription(?string $Description): static
    {
        $this->Description = $Description;

        return $this;
    }


    public function getRequestor(): ?User
    {
        return $this->requestor;
    }

    public function getNameRequestor(): ?string
    {
        return $this->requestor->getUsername();
    }

    public function getDepartementRequestor(): ?string
    {
        return $this->requestor->getDepartement();
    }

    public function setRequestor(?User $requestor): static
    {
        $this->requestor = $requestor;

        return $this;
    }

    public function getDecision(): ?string
    {
        return $this->Decision;
    }

    public function setDecision(string $Decision): static
    {
        $this->Decision = $Decision;

        return $this;
    }

    public function isStatus(): ?bool
    {
        return $this->status;
    }

    public function setStatus(bool $status): static
    {
        $this->status = $status;

        return $this;
    }

    public function getEx(): ?string
    {
        return $this->Ex;
    }

    public function setEx(?string $Ex): static
    {
        $this->Ex = $Ex;

        return $this;
    }


    public function isIndusRelated(): ?bool
    {
        return $this->Indus_Related;
    }

    public function setIndusRelated(?bool $Indus_Related): static
    {
        $this->Indus_Related = $Indus_Related;

        return $this;
    }

    public function getEngOwner(): ?User
    {
        return $this->Eng_Owner;
    }

    public function getUserNameEngOwner(): ?string
    {
        return $this->Eng_Owner !== null ? $this->Eng_Owner->getUsername() : '';
    }

    public function getNameEngOwner(): ?string
    {
        return $this->Eng_Owner !== null ? $this->Eng_Owner->getNom() : '';
    }

    public function getPrenomEngOwner(): ?string
    {
        return $this->Eng_Owner !== null ? $this->Eng_Owner->getPrenom() : '';
    }

    public function setEngOwner(?User $Eng_Owner): static
    {
        $this->Eng_Owner = $Eng_Owner;

        return $this;
    }

    public function getDateEnd(): ?\DateTimeInterface
    {
        return $this->date_end;
    }

    public function setDateEnd(?\DateTimeInterface $date_end): static
    {
        $this->date_end = $date_end;

        return $this;
    }

    public function getPrNumber(): ?int
    {
        return $this->Pr_Number;
    }

    public function setPrNumber(?int $Pr_Number): static
    {
        $this->Pr_Number = $Pr_Number;

        return $this;
    }

    /**
     * @return Collection<int, Commentaire>
     */
    public function getCommentaires(): Collection
    {
        return $this->commentaires;
    }

    public function addCommentaire(Commentaire $commentaire): static
    {
        if (!$this->commentaires->contains($commentaire)) {
            $this->commentaires->add($commentaire);
            $commentaire->setDmoId($this);
        }

        return $this;
    }

    public function removeCommentaire(Commentaire $commentaire): static
    {
        if ($this->commentaires->removeElement($commentaire)) {
            // set the owning side to null (unless already changed)
            if ($commentaire->getDmoId() === $this) {
                $commentaire->setDmoId(null);
            }
        }

        return $this;
    }

    public function getLastModificator(): ?User
    {
        return $this->last_Modificator;
    }

    public function setLastModificator(?User $last_Modificator): static
    {
        $this->last_Modificator = $last_Modificator;

        return $this;
    }

    public function getLastUpdateDate(): ?\DateTimeInterface
    {
        return $this->last_Update_Date;
    }

    public function setLastUpdateDate(\DateTimeInterface $last_Update_Date): static
    {
        $this->last_Update_Date = $last_Update_Date;

        return $this;
    }

    public function getExAssessment(): ?string
    {
        return $this->Ex_Assessment;
    }

    public function setExAssessment(?string $Ex_Assessment): static
    {
        $this->Ex_Assessment = $Ex_Assessment;

        return $this;
    }

    public function getSpentTime(): ?int
    {
        return $this->Spent_Time;
    }

    public function setSpentTime(int $Spent_Time): static
    {
        $this->Spent_Time = $Spent_Time;

        return $this;
    }

    public function getType(): ?string
    {
        return $this->Type;
    }

    public function setType(string $Type): static
    {
        $this->Type = $Type;

        return $this;
    }

    public function getDocument(): ?string
    {
        return $this->Document;
    }

    public function setDocument(?string $Document): static
    {
        $this->Document = $Document;

        return $this;
    }

    public function getProductRange(): ?ProductRange
    {
        return $this->productRange;
    }

    public function getNameDivisonProductRange(): ?string
    {
        return $this->productRange->getDivision();
    }

    public function getNameProductRange(): ?string
    {
        return $this->productRange->getProductRange();
    }
    public function setProductRange(?ProductRange $productRange): static
    {
        $this->productRange = $productRange;
        return $this;
    }

    public function getProjectRelation(): ?Project
    {
        return $this->project_relation;
    }

    public function setProjectRelation(?Project $project_relation): static
    {
        $this->project_relation = $project_relation;

        return $this;
    }

    /**
     * @return Collection<int, ReleasedPackage>
     */
    public function getReleasedPackages(): Collection
    {
        return $this->releasedPackages;
    }

    public function addReleasedPackage(ReleasedPackage $releasedPackage): static
    {
        if (!$this->releasedPackages->contains($releasedPackage)) {
            $this->releasedPackages->add($releasedPackage);
            $releasedPackage->addDmo($this);
        }

        return $this;
    }

    public function removeReleasedPackage(ReleasedPackage $releasedPackage): static
    {
        if ($this->releasedPackages->removeElement($releasedPackage)) {
            $releasedPackage->removeDmo($this);
        }

        return $this;
    }

    public function getLegacyId(): ?int
    {
        return $this->legacy_id;
    }

    public function setLegacyId(?int $legacy_id): static
    {
        $this->legacy_id = $legacy_id;
        return $this;
    }

    public function getDmo(): ?string
    {
        return $this->dmo;
    }

    public function setDmo(?string $dmo): static
    {
        $this->dmo = $dmo;
        return $this;
    }
}
